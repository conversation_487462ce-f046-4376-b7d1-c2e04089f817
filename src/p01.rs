use std::collections::HashMap;

use anyhow::Result;
use iroh_gossip::api::GossipTopic;
use sha2::Digest;

use crate::unix_minute;

trait SecretRotation {
    fn get_unix_minute_secret(&self, topic_hash: [u8; 32], unix_minute: u64, initial_secret_hash: [u8; 32]) -> [u8; 32];
}

// Default secret rotation function
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
struct DefaultSecretRotation;

impl SecretRotation for DefaultSecretRotation {
    fn get_unix_minute_secret(&self, topic_hash: [u8; 32], unix_minute: u64, initial_secret_hash: [u8; 32]) -> [u8; 32] {
        let mut hash = sha2::Sha512::new();
        hash.update(topic_hash);
        hash.update(unix_minute.to_be_bytes());
        hash.update(initial_secret_hash);
        hash.finalize()[..32].try_into().expect("hashing failed")
    }
}

#[derive(Debug, <PERSON><PERSON>)]
struct Topic<R: SecretRotation> {
    raw: String,
    hash: [u8; 32],                     // sha512( raw )[..32]
    initial_secret_hash: Vec<u8>, // (unixtime, hash(seed)) sha512( seed )[..32]
    secret_rotation_function: R,
}

impl<R: SecretRotation> Topic<R> {
    pub fn new(raw: String, initial_secret: &Vec<u8>, secret_rotation_function: Option<R>) -> Self {
        let mut raw_hash = sha2::Sha512::new();
        raw_hash.update(raw.as_bytes());

        let mut secret_seed_hash = sha2::Sha512::new();
        secret_seed_hash.update(initial_secret);


        Self {
            raw,
            hash: raw_hash.finalize()[..32]
                .try_into()
                .expect("hashing 'raw' failed"),
            initial_secret_hash: secret_seed_hash.finalize()[..32]
                .try_into()
                .expect("hashing 'initial_secret' failed"),
            secret_rotation_function: secret_rotation_function.unwrap_or_default(),
        }
    }

    /// Rotates secret seeds for the topic based on unix minute intervals.
    /// 
    /// This method provides basic key rotation functionality with a default
    /// implementation that can be customized. It calls the 
    ///
    /// # Security Notes
    /// 
    /// - Secret seeds are rotated to prevent replay attacks
    /// - Each unix minute gets its own unique seed hash
    /// - Old seeds are retained in the HashMap for historical verification
    pub fn set_unix_minute_secret_seed(&mut self, unix_minute: u64, new_secret_seed: &Vec<u8>) -> bool {
        let mut secret_seed_hash = sha2::Sha512::new();
        secret_seed_hash.update(new_secret_seed);
        
        self.current_secret.insert(
            unix_minute,
            secret_seed_hash.finalize()[..32]
                .try_into()
                .expect("hashing 'new_secret_seed' failed"),
        );
    }

    pub fn 
}

/// Trait for auto-discovery enabled Gossip functionality
pub trait AutoDiscoveryGossip {
    /// Subscribes to a topic and joins its network
    ///
    /// # Arguments
    ///
    /// * `topic_id` - The ID of the topic to subscribe to
    ///
    /// # Returns
    ///
    /// A Result containing the GossipTopic
    #[allow(async_fn_in_trait)]
    async fn subscribe_and_join_with_auto_discovery(&self, topic: Topic) -> Result<GossipTopic>;
}

impl AutoDiscoveryGossip for iroh_gossip::net::Gossip {
    /// Subscribes to a topic and joins its network
    ///
    /// # Arguments
    ///
    /// * `topic_id` - The ID of the topic to subscribe to
    ///
    /// # Returns
    ///
    /// A Result containing the GossipTopic
    async fn subscribe_and_join_with_auto_discovery(&self, topic: Topic) -> Result<GossipTopic> {
        let mut backoff_timer = 1 as u64;
        loop {
            let candidate_node_ids = self
                .topic_tracker
                .clone()
                .get_topic_nodes(&topic_id.into())
                .await?;
            let node_ids_cap = node_ids.as_slice().to_vec();

            if node_ids_cap.is_empty() {
                sleep(Duration::from_secs(backoff_timer)).await;
                backoff_timer += 1;
                continue;
            }
            return self
                .gossip
                .subscribe_and_join(topic_id, node_ids_cap.clone().into())
                .await
                .map_err(Into::into);
        }
    }
}
